﻿
using Amazon.Runtime.Internal.Util;
using Amazon.SimpleEmailV2;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using RealEstate.Application.AutoMapperProfiles;
using RealEstate.Application.Interfaces;
using RealEstate.Application.Services;
using RealEstate.Domain.Interfaces;
using RealEstate.Infrastructure;
using RealEstate.Infrastructure.Repositories;
using RealEstate.Infrastructure.Services;
using RealEstate.Infrastructure.Services.Notifications;
using RealEstate.InternalAPI.CustomAuthorizationPolicy;
using RealEstate.InternalAPI.Middleware;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.RateLimiting;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.InternalAPI
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            var connectionString = builder.Configuration.GetConnectionString("YezHomeConnection");

            if (connectionString == null)
            {
                throw new InvalidOperationException("Connection string 'YezHomeConnection' not found.");
            }

            var sameSiteModeString = builder.Configuration.GetValue<string>("CookieSettings:SameSite");
            var sameSiteMode = SameSiteMode.Lax;
            if (Enum.TryParse<SameSiteMode>(sameSiteModeString, true, out var parsedMode))
            {
                sameSiteMode = parsedMode;
            }

            builder.Services.Configure<CookiePolicyOptions>(options =>
            {
                // OnAppendCookie sẽ được gọi mỗi khi một cookie được thêm vào response
                options.OnAppendCookie = cookieContext =>
                {
                    // Áp dụng giá trị SameSite đã đọc từ cấu hình
                    cookieContext.CookieOptions.SameSite = sameSiteMode;

                    // Tự động thêm 'Secure = true' nếu là 'None', đây là quy tắc bắt buộc
                    if (sameSiteMode == SameSiteMode.None)
                    {
                        cookieContext.CookieOptions.Secure = true;
                    }
                };
            });

            builder.Services.AddLogging(loggingBuilder =>
            {
                loggingBuilder.AddSeq(builder.Configuration.GetSection("Seq"));
            });

            // Read Allowed Origins from appsettings.json
            var allowedOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>();
            Console.WriteLine("Allowed Origins: " + string.Join(", ", allowedOrigins ?? Array.Empty<string>()));

            builder.Services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", policy =>
                {
                    policy.WithOrigins(allowedOrigins ?? Array.Empty<string>())
                          .AllowCredentials()
                          .AllowAnyMethod()
                          .AllowAnyHeader();
                });
            });

            builder.Services.AddControllers().AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });

            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen();

            //add db context
            builder.Services.AddDbContext<ApplicationDbContext>(options => options.UseNpgsql(connectionString,
                o => o.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery)));

            builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    ValidateIssuer = true,
                    ValidateLifetime = true,
                    ValidateAudience = true,
                    ValidIssuer = builder.Configuration["JWT:Issuer"],
                    ValidAudience = builder.Configuration["JWT:Audience"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["JWT:Key"])),
                    ClockSkew = TimeSpan.Zero // Loại bỏ độ lệch thời gian mặc định 5 phút
                };

                options.Events = new JwtBearerEvents
                {
                    OnMessageReceived = context =>
                    {
                        var accessToken = context.Request.Cookies["_yh.utk"];
                        if (!string.IsNullOrEmpty(accessToken))
                        {
                            context.Token = accessToken;
                        }

                        return Task.CompletedTask;
                    }
                };
            });

            // Add AutoMapper
            builder.Services.AddAutoMapper(typeof(MappingProfile));

            // Rate limit configuration
            builder.Services.AddRateLimiter(options =>
            {
                options.AddFixedWindowLimiter("fixed", config =>
                {
                    config.PermitLimit = 30;
                    config.Window = TimeSpan.FromMinutes(1);
                    config.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
                    config.QueueLimit = 5;
                });
                options.RejectionStatusCode = StatusCodes.Status429TooManyRequests;
            });

            builder.Services.AddMemoryCache();

            // DI
            builder.Services.AddScoped<IAuthService, AuthService>();
            builder.Services.AddScoped<ITokenService, TokenService>();
            builder.Services.AddScoped<IUserAvatarService, UserAvatarService>();
            builder.Services.AddScoped<IUserService, UserService>();
            builder.Services.AddScoped<IUserDashboardService, UserDashboardService>();

            builder.Services.AddScoped<INotificationHistoryService, NotificationHistoryService>();
            builder.Services.AddScoped<INotificationPreferenceService, NotificationPreferenceService>();
            builder.Services.AddScoped<InAppNotificationService>();
            builder.Services.AddScoped<PushNotificationService>();
            builder.Services.AddScoped<EmailNotificationService>();
            builder.Services.AddScoped<INotificationService, CompositeNotificationService>();
            builder.Services.AddScoped<IEmailSender, AwsSesEmailSender>();

            builder.Services.AddScoped<IPropertyService, PropertyService>();
            builder.Services.AddScoped<IPropertyAnalyticsService, PropertyAnalyticsService>();

            builder.Services.AddScoped<IBlogService, BlogService>();
            builder.Services.AddScoped<IRolePermissionService, RolePermissionService>();

            builder.Services.AddScoped<IWalletService, WalletService>();
            builder.Services.AddScoped<IPaymentGatewayService, PaymentGatewayService>();

            builder.Services.AddScoped<IYezDataService, YezDataService>();
            builder.Services.AddScoped<ISettingService, SettingService>();

            builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
            builder.Services.AddScoped<IUserRepository, UserRepository>();
            builder.Services.AddScoped<IBlogRepository, BlogRepository>();
            builder.Services.AddScoped<IPropertyRepository, PropertyRepository>();
            builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
            builder.Services.AddScoped(typeof(IAuditableRepository<>), typeof(AuditableRepository<>));

            builder.Services.AddScoped<IAuthorizationHandler, HasPermissionHandler>();
            builder.Services.AddScoped<IAuthorizationHandler, UserAdminExistsHandler>();

            // AWS SES Configuration
            var sesAwsOptions = builder.Configuration.GetAWSOptions();

            // Configure SES AWS credentials from appsettings
            if (!string.IsNullOrEmpty(builder.Configuration["AWS:SES:AccessKey"]) &&
                !string.IsNullOrEmpty(builder.Configuration["AWS:SES:SecretKey"]))
            {
                sesAwsOptions.Credentials = new Amazon.Runtime.BasicAWSCredentials(
                    builder.Configuration["AWS:SES:AccessKey"],
                    builder.Configuration["AWS:SES:SecretKey"]
                );
            }

            // Set SES region if specified
            if (!string.IsNullOrEmpty(builder.Configuration["AWS:SES:Region"]))
            {
                sesAwsOptions.Region = Amazon.RegionEndpoint.GetBySystemName(builder.Configuration["AWS:SES:Region"]);
            }
            else if (!string.IsNullOrEmpty(builder.Configuration["AWS:Region"]))
            {
                sesAwsOptions.Region = Amazon.RegionEndpoint.GetBySystemName(builder.Configuration["AWS:Region"]);
            }

            // Configure SES service with specific credentials
            builder.Services.AddSingleton<IAmazonSimpleEmailServiceV2>(provider =>
            {
                return new AmazonSimpleEmailServiceV2Client(sesAwsOptions.Credentials, sesAwsOptions.Region);
            });

            // -----------------------------------------------------------------------------
            // Đăng ký Authorization Policies (Các quy tắc ủy quyền)
            // -----------------------------------------------------------------------------
            builder.Services.AddAuthorization(options =>
            {
                // Policy cơ sở: Đảm bảo người dùng tồn tại và đang hoạt động trong DB.
                // Policy này nên được áp dụng cho HẦU HẾT các API endpoint của Admin.
                options.AddPolicy("UserAdminExists", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                });

                // ----------------------------------------------------------
                // Policies dựa trên Permissions (bao gồm cả UserAdminExistsRequirement)
                // ----------------------------------------------------------

                // Quyền quản lý người dùng (ví dụ: tạo, sửa, xóa, phân quyền user)
                options.AddPolicy("CanManageUsers", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_USER_MANAGE_FULL.ToString()));
                });

                // Quyền xem màn hình quản lý người dùng
                options.AddPolicy("CanViewUserManagementScreen", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_VIEW_USER_MANAGEMENT_SCREEN.ToString()));
                });

                // Quyền duyệt bài đăng
                options.AddPolicy("CanApproveListing", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_LISTING_APPROVE.ToString()));
                });

                // Quyền quản lý (sửa/xóa) bài đăng
                options.AddPolicy("CanManageListing", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_LISTING_MANAGE.ToString()));
                });

                // Quyền xem giá bài đăng (ví dụ: riêng cho kế toán, admin tổng, không cho admin duyệt bài)
                options.AddPolicy("CanViewListingPrice", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_LISTING_VIEW_PRICE.ToString()));
                });

                // Quyền quản lý (thêm/sửa/xóa/viết) tin tức
                options.AddPolicy("CanManageNewsContent", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_NEWS_MANAGE.ToString()));
                });

                // Quyền xem màn hình soạn tin tức
                options.AddPolicy("CanViewNewsEditorScreen", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_VIEW_NEWS_EDITOR_SCREEN.ToString()));
                });

                // Quyền xem màn hình kế toán (dashboard)
                options.AddPolicy("CanViewAccounting", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_ACCOUNTING_VIEW.ToString()));
                });

                // Quyền quản lý các tác vụ kế toán (nếu có, ví dụ tạo giao dịch)
                options.AddPolicy("CanManageAccounting", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_ACCOUNTING_MANAGE.ToString()));
                });

                // Quyền xuất hóa đơn điện tử
                options.AddPolicy("CanExportInvoice", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_INVOICE_EXPORT_E.ToString()));
                });

                // Quyền xem báo cáo tài chính
                options.AddPolicy("CanViewFinancialReports", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_REPORT_FINANCE_VIEW.ToString()));
                });

                // Quyền xem yêu cầu hóa đơn của khách hàng
                options.AddPolicy("CanViewInvoiceRequests", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_INVOICE_REQUEST_VIEW.ToString()));
                });

                // ----------------------------------------------------------
                // Policies tổng hợp dựa trên Vai trò (Roles) và các Permissions của vai trò đó
                // (Kết hợp các quyền cụ thể đã định nghĩa ở trên)
                // ----------------------------------------------------------

                // Super Mod: Quyền cao nhất, bao gồm đầy đủ các quyền khác
                options.AddPolicy("IsSuperMod", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    // Sử dụng HasRoleRequirement nếu bạn có định nghĩa:
                    // policy.Requirements.Add(new HasRoleRequirement(UserRoleCode.SUPER_MOD.ToString()));
                    // Hoặc kiểm tra vai trò trực tiếp (nếu bạn đã thêm ClaimTypes.Role vào token):
                    policy.RequireRole(UserRoleCode.SUPER_MOD.ToString()); // Cách này đơn giản và hiệu quả
                });

                // System Admin: Tạo user, quản lý user, quản lý bài đăng, quản lý tin tức (không xem kế toán)
                options.AddPolicy("IsSystemAdmin", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_USER_MANAGE_FULL.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_LISTING_APPROVE.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_LISTING_MANAGE.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_NEWS_MANAGE.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_VIEW_USER_MANAGEMENT_SCREEN.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_VIEW_NEWS_EDITOR_SCREEN.ToString()));
                });

                // Admin Duyệt Bài: Chỉ duyệt bài đăng
                options.AddPolicy("IsAdminApprover", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_LISTING_APPROVE.ToString()));
                });

                // Admin Nội Dung: Quản lý và viết bài tin tức
                options.AddPolicy("IsAdminContent", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_NEWS_MANAGE.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_VIEW_NEWS_EDITOR_SCREEN.ToString()));
                });

                // Kế Toán Trưởng: Toàn quyền kế toán
                options.AddPolicy("IsFinanceManager", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_ACCOUNTING_VIEW.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_INVOICE_EXPORT_E.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_REPORT_FINANCE_VIEW.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_INVOICE_REQUEST_VIEW.ToString()));
                });

                // Kế Toán Viên: Kế toán cơ bản, không báo cáo tài chính
                options.AddPolicy("IsFinanceStaff", policy =>
                {
                    policy.Requirements.Add(new UserAdminExistsRequirement());
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_ACCOUNTING_VIEW.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_INVOICE_EXPORT_E.ToString()));
                    policy.Requirements.Add(new HasPermissionRequirement(PermissionCode.P_INVOICE_REQUEST_VIEW.ToString()));
                });
            });

            builder.Services.AddHealthChecks()
                .AddCheck("api", () => HealthCheckResult.Healthy("API is healthy"))
                .AddNpgSql(connectionString, name: "db");

            var healthCheckUrl = Environment.GetEnvironmentVariable("ASPNETCORE_URLS") ?? "http://localhost:8080";
            builder.Services.AddHealthChecksUI(setup =>
            {
                setup.AddHealthCheckEndpoint("YezHome API", $"{healthCheckUrl}/healthz");
            }).AddInMemoryStorage();

            // Configure Internal API Settings
            builder.Services.Configure<InternalApiSettings>(
                builder.Configuration.GetSection("InternalApiSettings"));


            var app = builder.Build();

            app.Use((context, next) =>
            {
                // Lấy ILogger instance
                var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();

                var properties = new Dictionary<string, object>
                {
                    ["Application"] = "YezHome-Internal-API"
                };

                using (logger.BeginScope(properties))
                {
                    return next();
                }
            });

            app.UseForwardedHeaders(new ForwardedHeadersOptions
            {
                ForwardedHeaders = ForwardedHeaders.XForwardedHost | ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedFor,
                KnownNetworks = { new Microsoft.AspNetCore.HttpOverrides.IPNetwork(IPAddress.Parse("**********"), 16) }
            });


            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
                app.UseDeveloperExceptionPage();
            }

            app.UseHttpsRedirection();

            // Apply security middleware for internal API endpoints
            app.UseLocalhostOnly();
            app.UseApiKeyAuth();

            app.UseRouting();

            app.UseCookiePolicy();

            app.UseCors("CorsPolicy");

            app.UseResponseCaching();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseRateLimiter(); // Apply rate limiting

            app.MapControllers();

            app.MapHealthChecks("/healthz", new HealthCheckOptions
            {
                // This is the important part to make the UI work
                Predicate = _ => true,
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });

            // This maps the UI dashboard to the /healthchecks-ui endpoint
            app.MapHealthChecksUI();

            app.Run();
        }
    }
}
