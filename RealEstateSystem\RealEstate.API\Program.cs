﻿using Amazon.S3;
using Amazon.SimpleEmailV2;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using RealEstate.API.Attributes;
using RealEstate.API.Configuration;
using RealEstate.API.CustomAuthorizationPolicy;
using RealEstate.API.Middleware;
using RealEstate.Application.AutoMapperProfiles;
using RealEstate.Application.Interfaces;
using RealEstate.Application.Services;
using RealEstate.Domain.Interfaces;
using RealEstate.Infrastructure;
using RealEstate.Infrastructure.Repositories;
using RealEstate.Infrastructure.Services;
using RealEstate.Infrastructure.Services.FileStorage;
using RealEstate.Infrastructure.Services.Notifications;
using RealEstate.Infrastructure.Services.PaymentGateway;
using Shared.Constants;
using Shared.Responses;
using SixLabors.ImageSharp;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.RateLimiting;

var builder = WebApplication.CreateBuilder(args);

var connectionString = builder.Configuration.GetConnectionString("YezHomeConnection");

if (connectionString == null)
{
    throw new InvalidOperationException("Connection string 'YezHomeConnection' not found.");
}

builder.Services.AddLogging(loggingBuilder =>
{
    loggingBuilder.AddSeq(builder.Configuration.GetSection("Seq"));
});

builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
    })
    .ConfigureApiBehaviorOptions(options =>
    {
        // Ghi đè lại hành vi mặc định khi ModelState không hợp lệ
        options.InvalidModelStateResponseFactory = context =>
        {
            var errors = context.ModelState
                .Where(e => e.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key, // Tên trường bị lỗi
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToList() // Danh sách lỗi
                );

            var apiResponse = ApiResponse<object>.Failure(ResponseMessages.ValidationError, errors);

            return new BadRequestObjectResult(apiResponse);
        };
    });
builder.Services.AddMemoryCache();


// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "API", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: 'Bearer {token}'",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });
});

//add db context
builder.Services.AddDbContext<ApplicationDbContext>(options => options.UseNpgsql(connectionString,
    o => o.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery)));

builder.Services.Configure<AnalysisFromFrontEnd>(
    builder.Configuration.GetSection("AnalysisFromFrontEnd"));

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(MappingProfile));

// Rate limit configuration
builder.Services.AddRateLimiter(options =>
{
    options.AddFixedWindowLimiter("fixed", config =>
    {
        config.PermitLimit = 30;
        config.Window = TimeSpan.FromMinutes(1);
        config.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
        config.QueueLimit = 5;
    });
    options.RejectionStatusCode = StatusCodes.Status429TooManyRequests;
});

// DI
// -----Services --
// Auth & User
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IUserAvatarService, UserAvatarService>();
builder.Services.AddScoped<IUserFavoriteService, UserFavoriteService>();
builder.Services.AddScoped<IUserDashboardService, UserDashboardService>();
builder.Services.AddScoped<INotificationPreferenceService, NotificationPreferenceService>();

// Property & Analytics
builder.Services.AddScoped<IPropertyService, PropertyService>();
builder.Services.AddScoped<IPropertyAnalyticsService, PropertyAnalyticsService>();

// Media & Image
builder.Services.AddScoped<IMediaServices, MediaServices>();
builder.Services.AddScoped<IImageProcessingService, ImageProcessingService>();

// File Storage Service - Configure based on appsettings
var storageProvider = builder.Configuration["Storage:Provider"] ?? "Local";
if (storageProvider.Equals("S3", StringComparison.OrdinalIgnoreCase))
{
    builder.Services.AddScoped<IFileStorageService, S3StorageService>();
    // S3 client will be configured later with specific credentials
}
else
{
    builder.Services.AddScoped<IFileStorageService, LocalStorageService>();
}

// Notification
builder.Services.AddScoped<INotificationHistoryService, NotificationHistoryService>();
builder.Services.AddScoped<InAppNotificationService>();
builder.Services.AddScoped<PushNotificationService>();
builder.Services.AddScoped<EmailNotificationService>();
builder.Services.AddScoped<INotificationService, CompositeNotificationService>();
builder.Services.AddScoped<IEmailSender, AwsSesEmailSender>();

// Blog & Contact
builder.Services.AddScoped<IBlogService, BlogService>();
builder.Services.AddScoped<IContactRequestService, ContactRequestService>();

// Wallet & Payment
builder.Services.AddScoped<IWalletService, WalletService>();
#if DEBUG
// Khi chạy ở chế độ Debug, sử dụng cổng thanh toán giả
builder.Services.AddScoped<IPaymentGatewayService, FakePaymentGatewayService>();
#else
// Khi build bản production, sử dụng cổng thanh toán thật
builder.Services.AddScoped<IPaymentGatewayService, FakePaymentGatewayService>();
#endif

// System & Settings
builder.Services.AddScoped<IYezDataService, YezDataService>();
builder.Services.AddScoped<ISettingService, SettingService>();

// AWS SES Configuration
var sesAwsOptions = builder.Configuration.GetAWSOptions();

// Configure SES AWS credentials from appsettings
if (!string.IsNullOrEmpty(builder.Configuration["AWS:SES:AccessKey"]) &&
    !string.IsNullOrEmpty(builder.Configuration["AWS:SES:SecretKey"]))
{
    sesAwsOptions.Credentials = new Amazon.Runtime.BasicAWSCredentials(
        builder.Configuration["AWS:SES:AccessKey"],
        builder.Configuration["AWS:SES:SecretKey"]
    );
}

// Set SES region if specified
if (!string.IsNullOrEmpty(builder.Configuration["AWS:SES:Region"]))
{
    sesAwsOptions.Region = Amazon.RegionEndpoint.GetBySystemName(builder.Configuration["AWS:SES:Region"]);
}
else if (!string.IsNullOrEmpty(builder.Configuration["AWS:Region"]))
{
    sesAwsOptions.Region = Amazon.RegionEndpoint.GetBySystemName(builder.Configuration["AWS:Region"]);
}

// Configure SES service with specific credentials
builder.Services.AddSingleton<IAmazonSimpleEmailServiceV2>(provider =>
{
    return new AmazonSimpleEmailServiceV2Client(sesAwsOptions.Credentials, sesAwsOptions.Region);
});

// AWS S3 Configuration (only if S3 storage is selected)
if (storageProvider.Equals("S3", StringComparison.OrdinalIgnoreCase))
{
    var s3AwsOptions = builder.Configuration.GetAWSOptions();

    // Configure S3 AWS credentials from appsettings
    if (!string.IsNullOrEmpty(builder.Configuration["AWS:S3:AccessKey"]) &&
        !string.IsNullOrEmpty(builder.Configuration["AWS:S3:SecretKey"]))
    {
        s3AwsOptions.Credentials = new Amazon.Runtime.BasicAWSCredentials(
            builder.Configuration["AWS:S3:AccessKey"],
            builder.Configuration["AWS:S3:SecretKey"]
        );
    }

    // Set S3 region if specified
    if (!string.IsNullOrEmpty(builder.Configuration["AWS:S3:Region"]))
    {
        s3AwsOptions.Region = Amazon.RegionEndpoint.GetBySystemName(builder.Configuration["AWS:S3:Region"]);
    }
    else if (!string.IsNullOrEmpty(builder.Configuration["AWS:Region"]))
    {
        s3AwsOptions.Region = Amazon.RegionEndpoint.GetBySystemName(builder.Configuration["AWS:Region"]);
    }

    // Configure S3 service with specific credentials
    builder.Services.AddSingleton<IAmazonS3>(provider =>
    {
        return new AmazonS3Client(s3AwsOptions.Credentials, s3AwsOptions.Region);
    });
}

//------Domain repository --
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IBlogRepository, BlogRepository>();
builder.Services.AddScoped<IPropertyRepository, PropertyRepository>();
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
builder.Services.AddScoped(typeof(IAuditableRepository<>), typeof(AuditableRepository<>));

//------Other DI --
builder.Services.AddScoped<IAuthorizationHandler, UserExistsHandler>();
builder.Services.AddScoped<EnforceFrontendOriginAttribute>();
builder.Services.AddScoped<IPasswordResetService, PasswordResetService>();

// Mapping configuration from appsetting
builder.Services.Configure<InternalEmailOptions>(
    builder.Configuration.GetSection("InternalEmail"));

// Configure Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["JWT:Issuer"],
        ValidAudience = builder.Configuration["JWT:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["JWT:Key"])),
    };
});

// Configure Authorization
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("UserExists", policy => policy.Requirements.Add(new UserExistsRequirement()));
});

// Read Allowed Origins from appsettings.json
var allowedOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>();

builder.Services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy", policy =>
    {
        policy.WithOrigins(allowedOrigins ?? Array.Empty<string>())
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.Services.Configure<ApiBehaviorOptions>(options =>
{
    options.SuppressModelStateInvalidFilter = false;
    options.InvalidModelStateResponseFactory = context =>
    {
        var errors = context.ModelState
            .Where(e => e.Value?.Errors.Count > 0)
            .ToDictionary(k => k.Key, v => v.Value?.Errors.Select(e => e.ErrorMessage).ToArray());

        return new BadRequestObjectResult(errors); // Return errors as a JSON object
    };
});

// Health Checks
builder.Services.AddHealthChecks()
    .AddCheck("api", () => HealthCheckResult.Healthy("API is healthy"))
    .AddNpgSql(connectionString, name: "db");

var healthCheckUrl = Environment.GetEnvironmentVariable("ASPNETCORE_URLS") ?? "http://localhost:8080";
builder.Services.AddHealthChecksUI(setup =>
{
    setup.AddHealthCheckEndpoint("YezHome API", $"{healthCheckUrl}/healthz");
}).AddInMemoryStorage();

var app = builder.Build();

app.Use((context, next) =>
{
    // Lấy ILogger instance
    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();

    var properties = new Dictionary<string, object>
    {
        ["Application"] = "YezHome-API"
    };

    using (logger.BeginScope(properties))
    {
        return next();
    }
});

app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.XForwardedHost | ForwardedHeaders.XForwardedProto | ForwardedHeaders.XForwardedFor,
    KnownNetworks = { new Microsoft.AspNetCore.HttpOverrides.IPNetwork(IPAddress.Parse("**********"), 16) }
});

app.UseMiddleware<ExceptionMiddleware>();
app.UseMiddleware<LoggingMiddleware>();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger(); 
    app.UseSwaggerUI();
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();

app.UseRouting();
app.UseCors("CorsPolicy");

app.UseResponseCaching();

app.UseAuthentication();
app.UseAuthorization();

app.UseRateLimiter();

app.MapControllers();

app.MapHealthChecks("/healthz", new HealthCheckOptions
{
    // This is the important part to make the UI work
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

// This maps the UI dashboard to the /healthchecks-ui endpoint
app.MapHealthChecksUI();

// Register background services
if (!app.Environment.IsDevelopment())
{
    // Only run in production to avoid conflicts during development
    app.Services.GetService<IHostApplicationLifetime>()?.ApplicationStarted.Register(() =>
    {
        app.Services.GetService<ILogger<Program>>()?.LogInformation("Starting background services");
    });

}

app.Run();
